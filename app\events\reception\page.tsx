"use client"

import { useState, useEffect, useMemo, Suspense } from "react"
import { useSearchParams } from 'next/navigation'
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Calendar, ChevronDown, ChevronUp, MapPin, Users } from "lucide-react"
import { fetchReceptionGuests, type ReceptionGuest } from "@/lib/strapi"
import AnimatedHero from "@/components/animated-hero"
import EnhancedMediaSlider from "@/components/enhanced-media-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

// Loading component
function ReceptionGuestsLoading() {
  return (
    <div className="container py-12">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array(6).fill(null).map((_, index) => (
          <Card key={`skeleton-${index}`} className="overflow-hidden">
            <div className="relative aspect-video">
              <Skeleton className="absolute inset-0" />
            </div>
            <CardContent className="p-6 space-y-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Main content component
function ReceptionGuestsContent() {
  const searchParams = useSearchParams()
  const [receptionGuests, setReceptionGuests] = useState<ReceptionGuest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        console.log('Reception Guests - Current language:', currentLang)
        console.log('Reception Guests - Strapi locale:', getStrapiLocale)

        // Use the original working API call with locale parameter
        const guestsData = await fetchReceptionGuests({
          populate: '*',
          locale: getStrapiLocale
        })

        console.log('Reception Guests - API response:', guestsData)
        console.log('Reception Guests - Number of items:', guestsData?.length)

        // Log each guest's ID to check for duplicates
        if (guestsData && guestsData.length > 0) {
          console.log('Reception Guests - IDs:', guestsData.map(g => g.id))

          // Remove duplicates based on ID before setting state
          const uniqueGuests = guestsData.filter((guest, index, self) =>
            index === self.findIndex(g => g.id === guest.id)
          )
          console.log('Reception Guests - After deduplication:', uniqueGuests.length)

          setReceptionGuests(uniqueGuests)
        } else {
          // If no data found with locale, try without locale as fallback
          console.log('No data found with locale, trying without locale...')
          try {
            const fallbackData = await fetchReceptionGuests({ populate: '*' })
            console.log('Reception Guests - Fallback API Response:', fallbackData)
            console.log('Reception Guests - Fallback Number of items:', fallbackData?.length)

            if (fallbackData && fallbackData.length > 0) {
              console.log('Reception Guests - Fallback IDs:', fallbackData.map(g => g.id))

              // Remove duplicates from fallback data too
              const uniqueFallbackGuests = fallbackData.filter((guest, index, self) =>
                index === self.findIndex(g => g.id === guest.id)
              )
              console.log('Reception Guests - Fallback After deduplication:', uniqueFallbackGuests.length)

              setReceptionGuests(uniqueFallbackGuests)
            } else {
              console.error('Reception guests data not found in fallback response:', fallbackData)
              setError(t('reception.noReceptionGuests'))
            }
          } catch (fallbackErr) {
            console.error('Fallback request error:', fallbackErr)
            setError(t('reception.noReceptionGuests'))
          }
        }
      } catch (err: any) {
        console.error('Error fetching reception guests:', err)
        setError(t('reception.error'))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  // Helper function to render structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined, isExpanded: boolean = false) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      const renderedContent = content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          const text = block.children.map(child => child.text).join('');
          if (text.trim() === '') return null;

          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      }).filter(Boolean);

      return <div className={isExpanded ? '' : 'line-clamp-3'}>{renderedContent}</div>;
    }

    return null;
  };

  if (error) {
    return (
      <div className="container py-12">
        <Card>
          <CardContent className="p-8">
            <div className="text-center py-12">
              <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
                <h2 className="text-xl font-semibold text-red-700 mb-2">
                  {t('common.error')}
                </h2>
                <p className="text-red-600">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return false;

    if (typeof content === 'string') {
      return content.length > 150;
    }

    if (Array.isArray(content)) {
      const totalText = content
        .map(block => block.children?.map(child => child.text).join('') || '')
        .join(' ');
      return totalText.length > 150;
    }

    return false;
  };

  const getAllMedia = (guest: ReceptionGuest): Array<{
    id: number;
    url: string;
    mime?: string;
    previewUrl?: string | null;
    alternativeText?: string | null;
  }> => {
    // Get media items, prioritizing 'images' array over 'image'
    let mediaItems: any[] = [];

    // First check if there's an 'images' array (multiple images)
    if (guest.images && guest.images.length > 0) {
      mediaItems = guest.images;
    }
    // If no 'images' array, check for 'image' (could be single or array)
    else if (guest.image) {
      if (Array.isArray(guest.image)) {
        mediaItems = guest.image;
      } else {
        // Single image object, convert to array
        mediaItems = [guest.image];
      }
    }

    // Remove duplicates based on ID and return properly typed array
    const uniqueMedia = mediaItems.filter((item, index, self) =>
      index === self.findIndex(t => t.id === item.id)
    );

    return uniqueMedia;
  }

  return (
    <div className="container py-12">
      {/* Introduction Card */}
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('reception.staticTitle')}</h2>
            <p>{t('reception.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>

      {/* Reception Guests Listing */}
      <section>
        {loading ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Array(6).fill(null).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden">
                <div className="relative aspect-video">
                  <Skeleton className="absolute inset-0" />
                </div>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <>
            {receptionGuests.length > 0 ? (
              <div className="space-y-6">
                <div className="border-b pb-2">
                  <h2 className="text-2xl font-bold">
                    <span className="text-primary">{t('reception.title')}</span>
                  </h2>
                  <p className="text-muted-foreground">{t('reception.description')}</p>
                </div>

                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                  {receptionGuests.map((guest) => {
                    const isExpanded = expandedCards[guest.id]
                    const showReadMore = shouldShowReadMore(guest.description)
                    const mediaItems = getAllMedia(guest)

                    return (
                      <Card key={guest.id} className="overflow-hidden hover:shadow-lg transition-all">
                        {/* Enhanced Media Slider for multiple images/videos */}
                        {mediaItems.length > 1 ? (
                          <EnhancedMediaSlider
                            media={mediaItems}
                            alt={guest.title || "Reception Guest"}
                            interval={3000}
                          />
                        ) : mediaItems.length === 1 ? (
                          <div className="relative aspect-video w-full min-h-[1px]">
                            {mediaItems[0].mime?.startsWith('video/') ? (
                              <video
                                src={mediaItems[0].url}
                                poster={mediaItems[0].previewUrl || undefined}
                                controls
                                className="w-full h-full object-contain"
                                playsInline
                              />
                            ) : (
                              <Image
                                src={mediaItems[0].url || '/placeholder.svg'}
                                alt={guest.title || "Reception Guest"}
                                fill
                                className="object-contain"
                                sizes="(max-width: 768px) 100vw, 33vw"
                              />
                            )}
                          </div>
                        ) : (
                          <div className="flex items-center justify-center aspect-video bg-gray-100">
                            <p className="text-muted-foreground p-4 text-center">No media available</p>
                          </div>
                        )}

                        <CardContent className="p-6">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                            <Calendar className="h-4 w-4" />
                            <time>{guest.date}</time>
                          </div>
                          <h3 className="text-xl font-bold mb-2">{guest.title}</h3>
                          <div className="space-y-2 mb-4">
                            {guest.guests && (
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <Users className="h-4 w-4" />
                                <span>{guest.guests}</span>
                              </div>
                            )}
                            {guest.origin && (
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{guest.origin}</span>
                              </div>
                            )}
                          </div>
                          <div className={`text-muted-foreground ${isExpanded ? '' : 'line-clamp-3'}`}>
                            {renderStructuredContent(guest.description, isExpanded)}
                          </div>

                          {showReadMore && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleExpanded(guest.id)}
                              className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                            >
                              {isExpanded ? (
                                <>
                                  {t('reception.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                </>
                              ) : (
                                <>
                                  {t('reception.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                </>
                              )}
                            </Button>
                          )}
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </div>
            ) : (
              <Card>
                <CardContent className="p-8">
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">{t('reception.noReceptionGuests')}</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </section>
    </div>
  )
}

// Main page component that wraps everything in Suspense
export default function ReceptionGuestsPage() {
  return (
    <Suspense fallback={<ReceptionGuestsLoading />}>
      <ReceptionGuestsPageContent />
    </Suspense>
  )
}

// Component that uses searchParams - wrapped in Suspense
function ReceptionGuestsPageContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="reception.title"
        description="reception.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <ReceptionGuestsContent />
    </div>
  )
}