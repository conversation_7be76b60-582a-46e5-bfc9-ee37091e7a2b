"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchReceptionGuests } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
// Badge import removed as it's no longer used
// import { Badge } from "@/components/ui/badge"
import { Calendar, ChevronDown, ChevronUp } from "lucide-react"
import ImageSlider from "@/components/image-slider"
// Users and Globe imports removed as they're no longer used
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { useSearchParams } from 'next/navigation'

interface ReceptionGuest {
  id: number
  documentId: string
  title: string
  date?: string
  guests?: string
  origin?: string
  description?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  image?: {
    id: number
    documentId: string
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  } | Array<{
    id: number
    documentId: string
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  }>
  images?: Array<{
    id: number
    documentId: string
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  }>
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
}

export default function ReceptionGuestsPage() {
  const [receptionGuests, setReceptionGuests] = useState<ReceptionGuest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})
  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  useEffect(() => {
    const loadReceptionGuests = async () => {
      try {
        setLoading(true)
        const response = await fetchReceptionGuests({
          populate: "*"
        })

        if (response && response.data) {
          // Log the reception guests data to debug the structure
          console.log("Reception guests data:", JSON.stringify(response.data, null, 2));

          // Extract reception guests from the response
          const guests = Array.isArray(response.data) ? response.data : [response.data];

          if (guests.length > 0) {
            setReceptionGuests(guests);
          } else {
            // If no guests are found, use fallback data
            setReceptionGuests(fallbackGuests);
          }
        } else {
          // If response is empty, use fallback data
          setReceptionGuests(fallbackGuests);
        }

        setLoading(false)
      } catch (err) {
        console.error("Error loading reception guests:", err)
        setError("Failed to load reception guests. Please try again later.")
        setLoading(false)

        // Fallback to static data if API fails
        setReceptionGuests(fallbackGuests)
      }
    }

    loadReceptionGuests()
  }, [])

  // Helper function to render structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined, isExpanded: boolean = false) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      const renderedContent = content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          const text = block.children.map(child => child.text).join('');
          if (text.trim() === '') return null;

          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      }).filter(Boolean);

      return <div className={isExpanded ? '' : 'line-clamp-3'}>{renderedContent}</div>;
    }

    return null;
  };

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return false;

    if (typeof content === 'string') {
      return content.length > 150;
    }

    if (Array.isArray(content)) {
      const totalText = content
        .map(block => block.children?.map(child => child.text).join('') || '')
        .join(' ');
      return totalText.length > 150;
    }

    return false;
  };

  // Fallback data in case the API fails
  const fallbackGuests: ReceptionGuest[] = [
    {
      id: 1,
      documentId: "fallback-1",
      title: "接待廣東省商務廳代表團",
      date: "2023-11-15",
      guests: "廣東省商務廳廳長張某及代表團成員",
      origin: "廣東省",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會接待了來自廣東省商務廳的代表團，雙方就粵港經貿合作、大灣區發展等議題進行了深入交流。代表團對九龍總商會在促進兩地商貿往來方面的貢獻表示讚賞。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-11-15T00:00:00.000Z",
      updatedAt: "2023-11-15T00:00:00.000Z",
      publishedAt: "2023-11-15T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 2,
      documentId: "fallback-2",
      title: "接待新加坡工商聯合總會訪問團",
      date: "2023-10-20",
      guests: "新加坡工商聯合總會會長李某及訪問團成員",
      origin: "新加坡",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會熱烈歡迎新加坡工商聯合總會訪問團。雙方就兩地商業合作、投資機會等議題進行了交流，並簽署了合作備忘錄，加強未來的合作關係。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-10-20T00:00:00.000Z",
      updatedAt: "2023-10-20T00:00:00.000Z",
      publishedAt: "2023-10-20T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 3,
      documentId: "fallback-3",
      title: "接待英國倫敦商會代表",
      date: "2023-09-05",
      guests: "英國倫敦商會國際事務總監史密斯先生",
      origin: "英國",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會接待了來自英國倫敦商會的代表。雙方就香港與英國的貿易關係、投資機會以及如何加強兩地商會合作等議題進行了discussion，為會員企業開拓更多國際商機。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-09-05T00:00:00.000Z",
      updatedAt: "2023-09-05T00:00:00.000Z",
      publishedAt: "2023-09-05T00:00:00.000Z",
      locale: "zh"
    }
  ]

  // Add helper functions for image handling
  const getImageUrl = (guest: ReceptionGuest) => {
    if (!guest.image) return "/placeholder.svg";

    if (Array.isArray(guest.image) && guest.image.length > 0) {
      const firstImage = guest.image[0];
      return firstImage.formats?.medium?.url ||
             firstImage.formats?.small?.url ||
             firstImage.url ||
             "/placeholder.svg";
    }

    // Handle single image case
    const singleImage = guest.image as {
      formats?: {
        medium?: { url: string },
        small?: { url: string }
      },
      url: string
    };

    return singleImage.formats?.medium?.url ||
           singleImage.formats?.small?.url ||
           singleImage.url ||
           "/placeholder.svg";
  }

  const getImageAlt = (guest: ReceptionGuest) => {
    if (!guest.image) return guest.title || "Reception Guest";

    if (Array.isArray(guest.image) && guest.image.length > 0) {
      return guest.image[0].alternativeText || guest.title || "Reception Guest";
    }

    // Handle single image case
    const singleImage = guest.image as {
      alternativeText: string | null
    };

    return singleImage.alternativeText || guest.title || "Reception Guest";
  }

  const getAllImages = (guest: ReceptionGuest) => {
    if (Array.isArray(guest.image)) {
      return guest.image;
    }
    if (guest.images && guest.images.length > 0) {
      return guest.images;
    }
    if (guest.image) {
      // Convert single image to array format
      const singleImage = guest.image as {
        id: number,
        documentId: string,
        name: string,
        alternativeText: string | null,
        formats: {
          large?: { url: string, width: number, height: number },
          medium?: { url: string, width: number, height: number },
          small?: { url: string, width: number, height: number },
          thumbnail?: { url: string, width: number, height: number }
        },
        url: string
      };
      return [singleImage];
    }
    return [];
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "",
          en: "Reception Guests",
        }}
        description={{
          zh: "九龍總商會作為香港重要的商業組織，經常接待來自世界各地的政商界重要賓客，促進香港與各地的商貿交流與合作。以下是我們近期接待的重要賓客。",
          en: "As an important business organization in Hong Kong, the Kowloon Chamber of Commerce frequently receives important guests from the political and business circles around the world, promoting trade exchanges and cooperation between Hong Kong and various regions. The following are the important guests we have received recently.",
        }}
        image="/placeholder.svg"
        height="large"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">Reception Guests</h2>
              <p>
                九龍總商會作為香港重要的商業組織，經常接待來自世界各地的政商界重要賓客，
                促進香港與各地的商貿交流與合作。以下是我們近期接待的重要賓客。
              </p>
              <p>
                As an important business organization in Hong Kong, the Kowloon Chamber of Commerce
                frequently receives important guests from the political and business circles around the
                world, promoting trade exchanges and cooperation between Hong Kong and various regions.
                The following are the important guests we have received recently.
              </p>
            </div>
          </CardContent>
        </Card>

        {loading ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <div className="relative aspect-video md:aspect-square">
                  <Skeleton className="h-full w-full" />
                </div>
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-3/4 mb-2" />
                  <Skeleton className="h-4 w-1/2 mb-4" />
                  <Skeleton className="h-20 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <div className="bg-red-50 text-red-500 p-4 rounded-md">{error}</div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {receptionGuests.map((guest) => {
              const isExpanded = expandedCards[guest.id]
              const showReadMore = shouldShowReadMore(guest.description)

              return (
                <Card key={guest.id} className="overflow-hidden">
                  <div className="relative aspect-video">
                    {getAllImages(guest).length > 1 ? (
                      <ImageSlider
                        images={getAllImages(guest)}
                        alt={getImageAlt(guest)}
                        interval={2000}
                      />
                    ) : (
                      <Image
                        src={getImageUrl(guest)}
                        alt={getImageAlt(guest)}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          console.error("Error loading image");
                          (e.target as HTMLImageElement).src = "/placeholder.svg";
                        }}
                      />
                    )}
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold mb-2">{guest.title}</h3>
                    <div className="flex items-center gap-2 text-muted-foreground mb-4">
                      <Calendar className="h-4 w-4" />
                      <span>{guest.date}</span>
                    </div>
                    <div className="prose prose-sm">
                      {renderStructuredContent(guest.description, isExpanded)}
                    </div>

                    {showReadMore && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleExpanded(guest.id)}
                        className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                      >
                        {isExpanded ? (
                          <>
                            <ChevronUp className="ml-1 h-4 w-4" />
                            {t('reception.readLess')}
                          </>
                        ) : (
                          <>
                            <ChevronDown className="ml-1 h-4 w-4" />
                            {t('reception.readMore')}
                          </>
                        )}
                      </Button>
                    )}
                  </CardContent>
                </Card>
              )
            })}
          </div>
        )}
      </div>
      {receptionGuests.length === 0 && !loading && !error && (
        <div className="text-center py-12">
          <p className="text-xl text-muted-foreground">{t('reception.noReceptionGuests')}</p>
        </div>
      )}
    </div>
  )
}