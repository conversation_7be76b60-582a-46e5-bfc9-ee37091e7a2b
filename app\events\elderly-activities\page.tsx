'use client'

import { useState, useEffect, useMemo } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, Heart, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import AnimatedHero from '@/components/animated-hero'
import { fetchElderlyActivities, type ElderlyActivity } from '@/lib/strapi'
import ImageSlider from '@/components/image-slider'
import { useTranslation, getLanguageFromParams } from '@/lib/translations'
import { useSearchParams } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import FullImageSlider from '@/components/full-image-slider'

// Mock data for elderly activities
const mockElderlyActivities = [
  {
    id: 1,
    title: "樂齡健康講座",
    titleEn: "Elderly Health Seminar",
    description: "邀請專業醫生為長者講解健康保健知識，包括飲食營養、運動保健等實用資訊。",
    descriptionEn: "Professional doctors share health knowledge for seniors, including nutrition, exercise and practical health information.",
    date: "2024-03-10",
    location: "九龍總商會多功能廳",
    locationEn: "KCC Multi-purpose Hall",
    participants: "150+ 長者會員",
    participantsEn: "150+ Senior Members",
    image: "/placeholder.svg"
  },
  {
    id: 2,
    title: "樂齡文化交流活動",
    titleEn: "Elderly Cultural Exchange Activity",
    description: "組織長者參觀博物館和文化景點，促進文化交流和社交互動。",
    descriptionEn: "Organizing seniors to visit museums and cultural sites, promoting cultural exchange and social interaction.",
    date: "2024-02-25",
    location: "香港歷史博物館",
    locationEn: "Hong Kong Museum of History",
    participants: "80+ 長者會員",
    participantsEn: "80+ Senior Members",
    image: "/placeholder.svg"
  }
]

export default function ElderlyActivitiesPage() {
  const searchParams = useSearchParams()
  const [activities, setActivities] = useState<ElderlyActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})
  const [expandedActivity, setExpandedActivity] = useState<number | null>(null)

  // Get current language and translation function
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Memoize translation function to prevent re-renders
  const memoizedT = useMemo(() => t, [currentLang])

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: string | undefined) => {
    return content && content.length > 100;
  }

  // Helper function to get image URL from Strapi image object
  const getImageUrl = (image: any) => {
    if (image.formats) {
      if (image.formats.medium?.url) return image.formats.medium.url;
      if (image.formats.small?.url) return image.formats.small.url;
      if (image.formats.large?.url) return image.formats.large.url;
      if (image.formats.thumbnail?.url) return image.formats.thumbnail.url;
    }
    return image.url || "/placeholder.svg";
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching elderly activities...')
        const strapiLocale = currentLang === 'zh' ? 'zh-Hant-HK' : currentLang === 'cn' ? 'zh-Hans-HK' : 'en';
        const activitiesData = await fetchElderlyActivities({ 
          populate: '*',
          locale: strapiLocale
        })
        console.log('Fetched elderly activities:', activitiesData)

        // Filter activities to only those matching the current locale
        const filteredActivities = (activitiesData || []).filter(activity => activity.locale === strapiLocale);
        // Deduplicate by documentId
        const uniqueActivities: typeof filteredActivities = [];
        const seenDocumentIds = new Set();
        for (const activity of filteredActivities) {
          if (!seenDocumentIds.has(activity.documentId)) {
            uniqueActivities.push(activity);
            seenDocumentIds.add(activity.documentId);
          }
        }
        if (uniqueActivities.length > 0) {
          setActivities(uniqueActivities)
        } else {
          setError(memoizedT('elderlyActivities.noActivities'))
        }
      } catch (err) {
        console.error('Error fetching elderly activities:', err)
        setError(memoizedT('elderlyActivities.error'))
      } finally {
          setLoading(false)
      }
    }

    fetchData()
  }, [currentLang, memoizedT])

  if (error) {
        return (
      <div className="min-h-screen">
        <AnimatedHero
          title="elderlyActivities.title"
          description="elderlyActivities.description"
          image="/placeholder.svg"
          lang={currentLang}
        />
        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {memoizedT('elderlyActivities.error')}
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="elderlyActivities.title"
        description="elderlyActivities.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      {/* Static introduction card */}
      <div className="container py-4">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{memoizedT('elderlyActivities.staticTitle')}</h2>
              <p>{memoizedT('elderlyActivities.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>
                  </div>

      <div className="container py-12">
        <div className="grid gap-8">
          {activities.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500">{memoizedT('elderlyActivities.noActivities')}</p>
                </div>
          ) : (
            <div className="grid gap-8">
              {activities.map((activity) => (
              <Card key={activity.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="relative aspect-video">
                        {(() => {
                          let images: any[] = [];
                          if (Array.isArray(activity.image)) {
                            images = activity.image.filter(Boolean);
                          } else if (activity.image) {
                            images = [activity.image];
                          }
                          return images.length > 0 ? (
                            <FullImageSlider
                              images={images}
                              alt={activity.activitytitle}
                            />
                          ) : (
                            <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
                              <p className="text-muted-foreground">No images</p>
                      </div>
                          );
                        })()}
                      </div>
                      <div className="p-6">
                        <div className="flex items-center gap-2 mb-4">
                          <Badge variant="secondary">{memoizedT('elderlyActivities.elderlyEvent')}</Badge>
                          <span className="text-sm text-gray-500">
                            {new Date(activity.date).toLocaleDateString(currentLang === 'zh' ? 'zh-HK' : 'en-HK')}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                          <MapPin className="h-4 w-4" />
                          <span>{activity.location}</span>
                        </div>
                        <h3 className="text-xl font-semibold mb-2">{activity.activitytitle}</h3>
                        <p className="text-gray-600 mb-4">{activity.description}</p>
                        <Button variant="outline" onClick={() => setExpandedActivity(expandedActivity === activity.id ? null : activity.id)}>
                          {expandedActivity === activity.id ? memoizedT('elderlyActivities.readLess') : memoizedT('elderlyActivities.readMore')}
                        </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
              ))}
              </div>
        )}
      </div>
    </div>
    </div>
  )
}
