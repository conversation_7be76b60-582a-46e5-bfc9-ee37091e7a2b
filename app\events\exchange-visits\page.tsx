"use client"

import { useState, useEffect, useMemo } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchExchangeVisits } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
// Badge import removed as it's no longer used
// import { Badge } from "@/components/ui/badge"
import { Calendar, MapPin, Users, ChevronDown, ChevronUp, ChevronLeft, ChevronRight } from "lucide-react"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { useSearchParams } from "next/navigation"

interface ExchangeVisit {
  id: number
  documentId: string
  title: string
  date?: string
  location?: string
  participants?: string
  description?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  image?: {
    id: number
    documentId: string
    name: string
    alternativeText: string | null
    formats: {
      large?: {
        url: string
        width: number
        height: number
      }
      medium?: {
        url: string
        width: number
        height: number
      }
      small?: {
        url: string
        width: number
        height: number
      }
      thumbnail?: {
        url: string
        width: number
        height: number
      }
    }
    url: string
  }
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
}

export default function ExchangeVisitsPage() {
  const [exchangeVisits, setExchangeVisits] = useState<ExchangeVisit[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [debugInfo, setDebugInfo] = useState<string>("");
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})
  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  useEffect(() => {
    const loadExchangeVisits = async () => {
      try {
        setLoading(true)
        const response = await fetchExchangeVisits({
          populate: "*"
        })

        if (response && response.data) {
          // Log the exchange visits data to debug the structure
          console.log("Exchange visits data:", JSON.stringify(response.data, null, 2));

          // Extract exchange visits from the response
          const visits = Array.isArray(response.data) ? response.data : [response.data];

          if (visits.length > 0) {
            setExchangeVisits(visits);
            setDebugInfo(`Found ${visits.length} exchange visits.`);
          } else {
            // If no visits are found, use fallback data
            setExchangeVisits(fallbackVisits);
            setDebugInfo("No exchange visits found in API response. Using fallback data.");
          }
        } else {
          // If response is empty, use fallback data
          setExchangeVisits(fallbackVisits);
          setDebugInfo("Empty API response. Using fallback data.");
        }

        setLoading(false)
      } catch (err) {
        console.error("Error loading exchange visits:", err)
        setError("Failed to load exchange visits. Please try again later.")
        setLoading(false)

        // Fallback to static data if API fails
        setExchangeVisits(fallbackVisits)
        setDebugInfo(`API error: ${err}. Using fallback data.`);
      }
    }

    loadExchangeVisits()
  }, [])

  // Helper function to get image URL
  const getImageUrl = (visit: ExchangeVisit) => {
    if (!visit.image) return "/placeholder.svg";
    return visit.image.formats?.medium?.url || visit.image.url || "/placeholder.svg";
  }

  // Helper function to get image alt text
  const getImageAlt = (visit: ExchangeVisit) => {
    return visit.image?.alternativeText || visit.title || "Exchange Visit";
  }

  // Helper function to render structured content
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined, isExpanded: boolean = false) => {
    if (!content) return null;

    // Handle string content
    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    // Handle structured content
    if (Array.isArray(content)) {
      const renderedContent = content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          const text = block.children.map(child => child.text).join('');
          if (text.trim() === '') return null;

          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      }).filter(Boolean);

      return <div className={isExpanded ? '' : 'line-clamp-3'}>{renderedContent}</div>;
    }

    return null;
  };

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return false;

    if (typeof content === 'string') {
      return content.length > 150;
    }

    if (Array.isArray(content)) {
      const totalText = content
        .map(block => block.children?.map(child => child.text).join('') || '')
        .join(' ');
      return totalText.length > 150;
    }

    return false;
  };

  // Fallback data in case the API fails
  const fallbackVisits: ExchangeVisit[] = [
    {
      id: 1,
      documentId: "fallback-1",
      title: "訪問廣州南沙自貿區",
      date: "2023-12-10",
      location: "廣州南沙自貿區",
      participants: "九龍總商會會長、副會長及20位會員代表",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會組織會員代表團訪問廣州南沙自貿區，考察當地的發展情況和投資環境。代表團參觀了多個重點項目，並與當地政府和企業代表進行了交流，探討合作機會。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-12-10T00:00:00.000Z",
      updatedAt: "2023-12-10T00:00:00.000Z",
      publishedAt: "2023-12-10T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 2,
      documentId: "fallback-2",
      title: "新加坡商業考察團",
      date: "2023-09-15",
      location: "新加坡",
      participants: "九龍總商會副會長及15位會員代表",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會組織會員赴新加坡進行商業考察，了解新加坡的商業環境和發展策略。考察團拜訪了新加坡貿易與工業部、新加坡企業發展局等機構，並與當地企業家進行了交流。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-09-15T00:00:00.000Z",
      updatedAt: "2023-09-15T00:00:00.000Z",
      publishedAt: "2023-09-15T00:00:00.000Z",
      locale: "zh"
    },
    {
      id: 3,
      documentId: "fallback-3",
      title: "訪問深圳前海合作區",
      date: "2023-07-20",
      location: "深圳前海",
      participants: "九龍總商會會長及25位會員代表",
      description: [
        {
          type: "paragraph",
          children: [
            {
              text: "九龍總商會組織會員代表團訪問深圳前海合作區，了解前海的最新發展和政策優勢。代表團與前海管理局進行了座談，並參觀了多個創新企業和項目，為會員企業尋找合作機會。",
              type: "text"
            }
          ]
        }
      ],
      createdAt: "2023-07-20T00:00:00.000Z",
      updatedAt: "2023-07-20T00:00:00.000Z",
      publishedAt: "2023-07-20T00:00:00.000Z",
      locale: "zh"
    }
  ]

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: "外訪交流",
          en: "Exchange Visits",
        }}
        description={{
          zh: "九龍總商會組織的外訪交流活動",
          en: "Exchange visits organized by the Kowloon Chamber of Commerce",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">外訪交流 / Exchange Visits</h2>
              <p>
                九龍總商會定期組織會員前往各地進行商務考察和交流，
                了解各地的商業環境和投資機會，為會員企業拓展商機。
                以下是我們近期組織的外訪交流活動。
              </p>
              <p>
                The Kowloon Chamber of Commerce regularly organizes members to conduct
                business inspections and exchanges in various places, to understand the
                business environment and investment opportunities in various places,
                and to expand business opportunities for member enterprises.
                The following are the exchange visits we have organized recently.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Error Message */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8">
            <p>{error}</p>
            {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
          </div>
        )}

        {loading ? (
          // Loading skeleton
          <div className="space-y-8">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="grid md:grid-cols-3 gap-0">
                    <Skeleton className="h-64 w-full" />
                    <div className="md:col-span-2 p-6 md:p-8">
                      <Skeleton className="h-8 w-3/4 mb-3" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-full mb-2" />
                      <Skeleton className="h-4 w-2/3 mb-6" />

                      <div className="grid sm:grid-cols-2 gap-4 mb-6">
                        <Skeleton className="h-6 w-40" />
                        <Skeleton className="h-6 w-40" />
                        <Skeleton className="h-6 w-full sm:col-span-2" />
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-8">
            {exchangeVisits.length > 0 ? (
              exchangeVisits.map((visit) => {
                const isExpanded = expandedCards[visit.id]
                const showReadMore = shouldShowReadMore(visit.description)

                return (
                  <Card key={visit.id} className="overflow-hidden hover:shadow-lg transition-all">
                    <CardContent className="p-0">
                      <div className="grid md:grid-cols-3 gap-0">
                        <div className="relative aspect-video md:aspect-square">
                          <Image
                            src={getImageUrl(visit)}
                            alt={getImageAlt(visit)}
                            fill
                            className="object-cover"
                          />
                          {/* Badge removed as requested */}
                        </div>
                        <div className="md:col-span-2 p-6 md:p-8">
                          <h3 className="text-2xl font-bold mb-3">{visit.title}</h3>
                          <div className="text-muted-foreground mb-6">
                            {renderStructuredContent(visit.description, isExpanded)}
                          </div>

                          {showReadMore && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleExpanded(visit.id)}
                              className="mb-4 p-0 h-auto text-primary hover:text-primary/80"
                            >
                              {isExpanded ? (
                                <>
                                  <ChevronUp className="ml-1 h-4 w-4" />
                                  {t('exchangeVisits.readLess')}
                                </>
                              ) : (
                                <>
                                  <ChevronDown className="ml-1 h-4 w-4" />
                                  {t('exchangeVisits.readMore')}
                                </>
                              )}
                            </Button>
                          )}

                          <div className="grid sm:grid-cols-2 gap-4 mb-6">
                            {visit.date && (
                              <div className="flex items-center gap-2">
                                <Calendar className="h-5 w-5 text-primary" />
                                <span>{visit.date}</span>
                              </div>
                            )}
                            {visit.location && (
                              <div className="flex items-center gap-2">
                                <MapPin className="h-5 w-5 text-primary" />
                                <span>{visit.location}</span>
                              </div>
                            )}
                            {visit.participants && (
                              <div className="flex items-center gap-2 sm:col-span-2">
                                <Users className="h-5 w-5 text-primary" />
                                <span>{visit.participants}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })
            ) : (
              <div className="text-center py-12">
                <p className="text-xl text-muted-foreground">{t('exchangeVisits.noExchangeVisits')}</p>
                {debugInfo && <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}