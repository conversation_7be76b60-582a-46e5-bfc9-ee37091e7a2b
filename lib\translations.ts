import enTranslations from '@/locales/en.json';
import zhTranslations from '@/locales/zh.json';
import cnTranslations from '@/locales/cn.json';

export type Language = 'en' | 'zh' | 'cn';

export type TranslationKey =
  | 'common.loading'
  | 'common.error'
  | 'common.readMore'
  | 'common.loadMore'
  | 'common.noData'
  | 'common.refresh'
  | 'common.download'
  | 'common.csv'
  | 'navigation.home'
  | 'navigation.about'
  | 'navigation.events'
  | 'navigation.contact'
  | 'navigation.latestNews'
  | 'navigation.whatIsKcc'
  | 'navigation.managementTeam'
  | 'navigation.ourChamber'
  | 'navigation.chamberOrg'
  | 'navigation.councilMember'
  | 'navigation.pastCouncils'
  | 'navigation.affiliatedAssociation'
  | 'navigation.kccDevelopment'
  | 'navigation.milestones'
  | 'navigation.upcomingEvents'
  | 'navigation.chamberActivities'
  | 'navigation.reception'
  | 'navigation.monthlyMeetings'
  | 'navigation.exchangeVisits'
  | 'navigation.affiliatedActivities'
  | 'navigation.socialWelfareActivities'
  | 'navigation.otherActivities'
  | 'navigation.newsCenter'
  | 'navigation.joinAssociation'
  | 'navigation.suggestionBox'
  | 'navigation.contactUs'
  | 'navigation.rentalUnits'
  | 'activities.newEra'
  | 'activities.elderlyActivities'
  | 'activities.youthFestival'
  | 'activities.forums'
  | 'activities.careTeam'
  | 'activities.elderlyCenter'
  | 'activities.seaScouts'
  | 'header.welcome'
  | 'header.login'
  | 'header.register'
  | 'header.logout'
  | 'header.companyName'
  | 'header.companyNameShort'
  | 'hero.title'
  | 'hero.description'
  | 'news.title'
  | 'news.description'
  | 'news.center'
  | 'news.centerDescription'
  | 'news.latest'
  | 'news.latestDescription'
  | 'news.archive'
  | 'news.archiveDescription'
  | 'news.noNews'
  | 'news.readMore'
  | 'news.loadMore'
  | 'news.loading'
  | 'news.backToNews'
  | 'admin.contactSubmissions'
  | 'admin.totalRegistrations'
  | 'admin.events'
  | 'admin.downloadCsv'
  | 'admin.noDataToDownload'
  | 'admin.noRegistrationsForEvent'
  | 'admin.loadingRegistrations'
  | 'admin.checkApiConfig'
  | 'admin.refreshPage'
  | 'forms.name'
  | 'forms.email'
  | 'forms.phone'
  | 'forms.organization'
  | 'forms.submittedDate'
  | 'forms.eventName'
  | 'management.title'
  | 'management.description'
  | 'management.teamTitle'
  | 'management.teamDescription'
  | 'management.error'
  | 'management.noMembers'
  | 'management.readMore'
  | 'management.readLess'
  | 'introduction.heroTitle'
  | 'introduction.heroDescription'
  | 'introduction.staticTitle'
  | 'introduction.staticDescription'
  | 'introduction.error'
  | 'introduction.noContent'
  | 'introduction.readMore'
  | 'introduction.readLess'
  | 'chamber.latestNews'
  | 'chamber.whatIsKCC'
  | 'chamber.events'
  | 'chamber.noNewsAvailable'
  | 'chamber.noUpcomingEvents'
  | 'chamber.viewMoreNews'
  | 'chamber.viewMoreEvents'
  | 'chamber.learnMore'
  | 'chamber.latest'
  | 'ourChamber.title'
  | 'ourChamber.description'
  | 'ourChamber.staticTitle'
  | 'ourChamber.staticDescription'
  | 'ourChamber.error'
  | 'ourChamber.noContent'
  | 'ourChamber.readMore'
  | 'ourChamber.readLess'
  | 'chamberOrg.title'
  | 'chamberOrg.description'
  | 'chamberOrg.staticTitle'
  | 'chamberOrg.staticDescription'
  | 'chamberOrg.error'
  | 'chamberOrg.noContent'
  | 'chamberOrg.readMore'
  | 'chamberOrg.readLess'
  | 'councilMember.title'
  | 'councilMember.description'
  |  'councilMember.staticTitle'
  | 'councilMember.staticDescription'
  | 'councilMember.error'
  | 'councilMember.noContent'
  | 'councilMember.noMembers'
  | 'councilMember.viewDetails'
  | 'councilMember.position'
  | 'pastCouncils.title'
  | 'pastCouncils.description'
  | 'pastCouncils.staticTitle'
  | 'pastCouncils.staticDescription'
  | 'pastCouncils.error'
  | 'pastCouncils.noContent'
  | 'pastCouncils.noCouncilors'
  | 'pastCouncils.viewDetails'
  | 'pastCouncils.position'
  | 'pastCouncils.year'
  | 'affiliatedAssociation.title'
  | 'affiliatedAssociation.description'
  | 'affiliatedAssociation.staticTitle'
  | 'affiliatedAssociation.staticDescription'
  | 'affiliatedAssociation.error'
  | 'affiliatedAssociation.noContent'
  | 'affiliatedAssociation.noAssociations'
  | 'affiliatedAssociation.readMore'
  | 'affiliatedAssociation.readLess'
  | 'joinUs.title'
  | 'joinUs.description'
  | 'joinUs.staticTitle'
  | 'joinUs.staticDescription'
  | 'joinUs.error'
  | 'joinUs.noContent'
  | 'joinUs.noData'
  | 'joinUs.noImage'
  | 'joinUs.requirements'
  | 'joinUs.benefits'
  | 'joinUs.applicationProcess'
  | 'joinUs.readMore'
  | 'joinUs.readLess'
  | 'joinUs.showingSteps'
  | 'joinUs.visitWebsite'
  | 'developmentHistory.title'
  | 'developmentHistory.description'
  | 'developmentHistory.staticTitle'
  | 'developmentHistory.staticDescription'
  | 'developmentHistory.milestonesTitle'
  | 'developmentHistory.milestonesDescription'
  | 'developmentHistory.error'
  | 'developmentHistory.noContent'
  | 'developmentHistory.noHistory'
  | 'developmentHistory.readMore'
  | 'developmentHistory.readLess'
  | 'developmentHistory.year'
  | 'developmentHistory.location'
  | 'developmentHistory.participants'
  | 'contact.title'
  | 'contact.description'
  | 'contact.staticTitle'
  | 'contact.staticDescription'
  | 'contact.contactInfo'
  | 'contact.contactForm'
  | 'contact.email'
  | 'contact.phone'
  | 'contact.fax'
  | 'contact.address'
  | 'contact.officeHours'
  | 'contact.mondayToFriday'
  | 'contact.saturday'
  | 'contact.sundayHolidays'
  | 'contact.name'
  | 'contact.companyName'
  | 'contact.phoneNumber'
  | 'contact.subject'
  | 'contact.message'
  | 'contact.required'
  | 'contact.submit'
  | 'contact.submitting'
  | 'contact.submitSuccess'
  | 'contact.submitError'
  | 'contact.namePlaceholder'
  | 'contact.companyPlaceholder'
  | 'contact.emailPlaceholder'
  | 'contact.phonePlaceholder'
  | 'contact.subjectPlaceholder'
  | 'contact.messagePlaceholder'
  | 'suggestionBox.title'
  | 'suggestionBox.description'
  | 'suggestionBox.staticTitle'
  | 'suggestionBox.staticDescription'
  | 'suggestionBox.formTitle'
  | 'suggestionBox.suggestionTitle'
  | 'suggestionBox.suggestionMessage'
  | 'suggestionBox.category'
  | 'suggestionBox.name'
  | 'suggestionBox.email'
  | 'suggestionBox.date'
  | 'suggestionBox.required'
  | 'suggestionBox.submit'
  | 'suggestionBox.submitting'
  | 'suggestionBox.submitSuccess'
  | 'suggestionBox.submitError'
  | 'suggestionBox.titlePlaceholder'
  | 'suggestionBox.messagePlaceholder'
  | 'suggestionBox.categoryPlaceholder'
  | 'suggestionBox.namePlaceholder'
  | 'suggestionBox.emailPlaceholder'
  | 'suggestionBox.generalCategory'
  | 'suggestionBox.serviceCategory'
  | 'suggestionBox.eventCategory'
  | 'suggestionBox.otherCategory'
  | 'rental.title'
  | 'rental.description'
  | 'rental.staticTitle'
  | 'rental.staticDescription'
  | 'rental.error'
  | 'rental.noData'
  | 'rental.noContent'
  | 'rental.readMore'
  | 'rental.readLess'
  | 'rental.loadMore'
  | 'rental.loading'
  | 'rental.location'
  | 'rental.area'
  | 'rental.contact'
  | 'rental.email'
  | 'rental.phone'
  | 'rental.noImages'
  | 'rental.features'
  | 'milestones.title'
  | 'milestones.description'
  | 'milestones.staticTitle'
  | 'milestones.staticDescription'
  | 'milestones.error'
  | 'milestones.noContent'
  | 'milestones.noMilestones'
  | 'milestones.readMore'
  | 'milestones.readLess'
  | 'newEra.title'
  | 'newEra.description'
  | 'newEra.staticTitle'
  | 'newEra.staticDescription'
  | 'newEra.activitiesTitle'
  | 'newEra.activitiesDescription'
  | 'newEra.error'
  | 'newEra.noContent'
  | 'newEra.noActivities'
  | 'newEra.readMore'
  | 'newEra.readLess'
  | 'newEra.loading'
  | 'newEra.newMemberEvent'
  | 'youthFestival.title'
  | 'youthFestival.description'
  | 'youthFestival.staticTitle'
  | 'youthFestival.staticDescription'
  | 'youthFestival.error'
  | 'youthFestival.noContent'
  | 'youthFestival.noActivities'
  | 'youthFestival.readMore'
  | 'youthFestival.readLess'
  | 'youthFestival.loading'
  | 'youthFestival.youthEvent'
  | 'youthFestival.membersInvolved'
  | 'elderlyActivities.title'
  | 'elderlyActivities.description'
  | 'elderlyActivities.staticTitle'
  | 'elderlyActivities.staticDescription'
  | 'elderlyActivities.error'
  | 'elderlyActivities.noContent'
  | 'elderlyActivities.noActivities'
  | 'elderlyActivities.readMore'
  | 'elderlyActivities.readLess'
  | 'elderlyActivities.loading'
  | 'elderlyActivities.elderlyEvent'
  | 'upcomingEvents.title'
  | 'upcomingEvents.description'
  | 'upcomingEvents.staticTitle'
  | 'upcomingEvents.staticDescription'
  | 'upcomingEvents.error'
  | 'upcomingEvents.noContent'
  | 'upcomingEvents.noEvents'
  | 'upcomingEvents.readMore'
  | 'upcomingEvents.readLess'
  | 'upcomingEvents.loading'
  | 'upcomingEvents.registerNow'
  | 'upcomingEvents.visitWebsite'
  | 'upcomingEvents.loginRequired'
  | 'upcomingEvents.loginRequiredDesc'
  | 'upcomingEvents.cancel'
  | 'upcomingEvents.loginNow'
  | 'exchangeVisits.readMore'
  | 'exchangeVisits.readLess'
  | 'exchangeVisits.noExchangeVisits'
  | 'reception.title'
  | 'reception.description'
  | 'reception.staticTitle'
  | 'reception.staticDescription'
  | 'reception.error'
  | 'reception.noContent'
  | 'reception.noReceptionGuests'
  | 'reception.readMore'
  | 'reception.readLess'
  | 'reception.loading'
  | 'reception.date'
  | 'reception.guests'
  | 'reception.origin'
  | 'reception.location'
  | 'reception.participants'
  | 'footer.email'
  | 'footer.phone'
  | 'footer.address'
  | 'footer.copyright'
  | 'footer.yearsOfHistory'
  | 'footer.corporateMembers'
  | 'footer.annualEvents'
  | 'footer.facebook'
  | 'footer.wechat'
  | 'forums.title'
  | 'forums.description'
  | 'forums.staticTitle'
  | 'forums.staticDescription'
  | 'forums.sectionTitle'
  | 'forums.sectionSubtitle'
  | 'forums.error'
  | 'forums.noForums'
  | 'forums.forumDiscussion';

const translations = {
  en: enTranslations,
  zh: zhTranslations,
  cn: cnTranslations,
};

/**
 * Get translation for a specific key and language
 * @param key - The translation key (e.g., 'common.loading')
 * @param lang - The language code ('en', 'zh', 'cn')
 * @returns The translated text
 */
export function getTranslation(key: TranslationKey, lang: Language = 'en'): string {
  const langTranslations = translations[lang] || translations.en;

  // Split the key by dots to navigate nested objects
  const keys = key.split('.');
  let result: any = langTranslations;

  for (const k of keys) {
    result = result?.[k];
    if (result === undefined) {
      // Fallback to English if translation not found
      result = translations.en;
      for (const fallbackKey of keys) {
        result = result?.[fallbackKey];
        if (result === undefined) {
          console.warn(`Translation not found for key: ${key}`);
          return key; // Return the key itself as fallback
        }
      }
      break;
    }
  }

  return result || key;
}

/**
 * Get translation object for multiple keys
 * @param keys - Array of translation keys
 * @param lang - The language code
 * @returns Object with translated values
 */
export function getTranslations(keys: TranslationKey[], lang: Language = 'en'): Record<string, string> {
  const result: Record<string, string> = {};
  keys.forEach(key => {
    result[key] = getTranslation(key, lang);
  });
  return result;
}

/**
 * Hook-like function to get translation function for a specific language
 * @param lang - The language code
 * @returns Translation function
 */
export function useTranslation(lang: Language = 'en') {
  return {
    t: (key: TranslationKey) => getTranslation(key, lang),
    lang,
  };
}

/**
 * Get language from URL search params or default
 * @param searchParams - URLSearchParams or string
 * @returns Language code
 */
export function getLanguageFromParams(searchParams: URLSearchParams | string | null): Language {
  let lang: string | null = null;

  if (typeof searchParams === 'string') {
    lang = searchParams;
  } else if (searchParams instanceof URLSearchParams) {
    lang = searchParams.get('lang');
  }

  switch (lang) {
    case 'zh':
      return 'zh';
    case 'cn':
      return 'cn';
    case 'en':
      return 'en';
    default:
      return 'en';
  }
}

/**
 * Create bilingual text (Chinese / English)
 * @param zhKey - Chinese translation key
 * @param enKey - English translation key (optional, defaults to same as zhKey)
 * @param lang - Current language
 * @returns Bilingual text string
 */
export function getBilingualText(
  zhKey: TranslationKey,
  enKey?: TranslationKey,
  lang: Language = 'en'
): string {
  const zhText = getTranslation(zhKey, 'zh');
  const enText = getTranslation(enKey || zhKey, 'en');

  if (lang === 'en') {
    return `${enText} / ${zhText}`;
  }
  return `${zhText} / ${enText}`;
}

/**
 * Build URL with language parameter
 * @param path - The base path (e.g., '/news', '/about')
 * @param lang - The language code
 * @returns URL with language parameter if not default
 */
export function buildUrlWithLang(path: string, lang: Language = 'en'): string {
  // Don't add lang parameter for default language (English)
  if (lang === 'en') {
    return path;
  }

  // Check if path already has query parameters
  const separator = path.includes('?') ? '&' : '?';
  return `${path}${separator}lang=${lang}`;
}

/**
 * Get current language from search params and build URL
 * @param path - The target path
 * @param searchParams - Current URL search params
 * @returns URL with preserved language parameter
 */
export function buildUrlWithCurrentLang(path: string, searchParams: URLSearchParams | null): string {
  if (!searchParams) {
    return path;
  }

  const currentLang = getLanguageFromParams(searchParams);
  return buildUrlWithLang(path, currentLang);
}