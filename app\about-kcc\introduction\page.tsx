"use client"

import { useState, useEffect, Suspense, useMemo, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { fetchWhatIsKCCs, type WhatIsKCCData } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"

// Strapi image interfaces
interface StrapiImageFormat {
  ext: string
  url: string
  hash: string
  mime: string
  name: string
  path: string | null
  size: number
  width: number
  height: number
  sizeInBytes: number
  provider_metadata?: {
    public_id: string
    resource_type: string
  }
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId?: string
  name?: string
  url?: string
  formats?: StrapiImageFormats
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  hash?: string
  ext?: string
  mime?: string
  size?: number
  previewUrl?: string | null
  provider?: string
  provider_metadata?: {
    public_id: string
    resource_type: string
  }
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: any[]): any[] {
  return mediaArr.map((item: any) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

// Local FullImageSlider for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: any[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

function WhatIsKCCContent() {
  const searchParams = useSearchParams()
  const [whatIsKCCData, setWhatIsKCCData] = useState<WhatIsKCCData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set())

  // Get current language and translation function
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  // Memoize the Strapi locale to prevent unnecessary re-calculations
  const strapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  // Helper function to get image URL from Strapi image object
  const getImageUrl = (strapiImage: StrapiImage): string => {
    if (!strapiImage) return "/placeholder.svg";

    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

    // For videos, return the preview URL if available
    if (strapiImage.mime?.startsWith('video/')) {
      return strapiImage.previewUrl || "/placeholder.svg";
    }

    // For images, try different format sizes
    if (strapiImage.formats?.medium?.url) {
      return strapiImage.formats.medium.url.startsWith('http')
        ? strapiImage.formats.medium.url
        : `${strapiUrl}${strapiImage.formats.medium.url}`;
    }
    if (strapiImage.formats?.small?.url) {
      return strapiImage.formats.small.url.startsWith('http')
        ? strapiImage.formats.small.url
        : `${strapiUrl}${strapiImage.formats.small.url}`;
    }
    if (strapiImage.url) {
      return strapiImage.url.startsWith('http')
        ? strapiImage.url
        : `${strapiUrl}${strapiImage.url}`;
    }
    // Fallback to placeholder
    return "/placeholder.svg";
  }

  // Toggle expanded state for content
  const toggleExpanded = (itemId: number) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId)
    } else {
      newExpanded.add(itemId)
    }
    setExpandedItems(newExpanded)
  }

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setLoading(true)

        const response = await fetchWhatIsKCCs({
          populate: "*",
          locale: strapiLocale // Add locale parameter
        })

        console.log("What is KCC API Response:", response)

        if (response.data && Array.isArray(response.data) && response.data.length > 0) {
          setWhatIsKCCData(response.data)
        } else {
          setError(t('introduction.noContent'))
        }
      } catch (err: any) {
        console.error("Error fetching What is KCC content:", err)
        setError(err.message || t('introduction.error'))
      } finally {
        setLoading(false)
      }
    }

    fetchContent()
  }, [currentLang]) // Only re-run when the actual language changes

  // Function to render rich text content with read more functionality
  const renderContent = (content: string, itemId: number) => {
    if (!content) return null

    const isExpanded = expandedItems.has(itemId)
    const maxLength = 300 // Characters to show before "Read More"

    // Split content by newlines and render as paragraphs
    const paragraphs = content.split('\n').filter(p => p.trim())
    const fullText = paragraphs.join(' ')

    const shouldTruncate = fullText.length > maxLength
    const displayText = shouldTruncate && !isExpanded
      ? fullText.substring(0, maxLength) + '...'
      : fullText

    return (
      <div>
        <div className="space-y-4">
          {displayText.split('\n').map((paragraph, index) => (
            <p key={index} className="mb-4">
              {paragraph.trim()}
            </p>
          ))}
        </div>
        {shouldTruncate && (
          <Button
            variant="link"
            onClick={() => toggleExpanded(itemId)}
            className="p-0 h-auto text-blue-600 hover:text-blue-800"
          >
            {isExpanded ? t('introduction.readLess') : t('introduction.readMore')}
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title="introduction.heroTitle"
        description="introduction.heroDescription"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <div className="container py-12">
        {/* Static Introduction Section */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{t('introduction.staticTitle')}</h2>
              <p>
                {t('introduction.staticDescription')}
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Dynamic Content from Strapi */}
        <Card className="overflow-hidden">
          <CardContent className="p-8">
            {loading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
                <Skeleton className="h-8 w-1/2 mt-8" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-full" />
              </div>
            ) : error ? (
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                {error}
              </div>
            ) : whatIsKCCData.length > 0 ? (
              <div className="space-y-8">
                {whatIsKCCData.map((item, index) => (
                  <div key={item.id || index} className="prose prose-lg max-w-none">
                    <h1 className="text-3xl font-bold mb-6">{item.title}</h1>
                    <div className="grid md:grid-cols-2 gap-8 items-start">
                      <div className="space-y-4">
                        {renderContent(item.whatiskccframe, item.id)}
                      </div>
                      <div className="relative mt-6 md:mt-0">
                        {(() => {
                          // Prepare images array for the slider
                          const mediaList = patchMediaMime(Array.isArray(item.image) ? item.image : (item.image ? [item.image] : []));

                          if (mediaList.length > 1) {
                            return <FullImageSlider images={mediaList} alt={item.title || "What is KCC Media"} interval={3000} />
                          } else if (mediaList.length === 1) {
                            return (
                              <div className="relative aspect-[4/3] w-full min-h-[1px]">
                                {mediaList[0].mime?.startsWith('video/') ? (
                                  <video
                                    src={mediaList[0].url}
                                    poster={mediaList[0].previewUrl}
                                    controls
                                    className="w-full h-full object-contain"
                                    playsInline
                                  />
                                ) : (
                                  <Image
                                    src={getImageUrl(mediaList[0])}
                                    alt={item.title || "What is KCC Image"}
                                    fill
                                    className="object-contain"
                                    sizes="(max-width: 768px) 100vw, 50vw"
                                  />
                                )}
                              </div>
                            )
                          } else {
                            return (
                              <div className="flex items-center justify-center aspect-[4/3] bg-gray-100">
                                <p className="text-muted-foreground p-4 text-center">No media available</p>
                              </div>
                            )
                          }
                        })()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-gray-500">{t('introduction.noContent')}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Loading component for Suspense fallback
function WhatIsKCCLoading() {
  return (
    <div className="min-h-screen">
      <div className="relative h-96 bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
        <div className="text-center text-white">
          <h1 className="text-4xl font-bold mb-4">What is KCC</h1>
          <p className="text-xl">Loading...</p>
        </div>
      </div>
      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4 mx-auto" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6 mx-auto" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-8">
            <div className="space-y-4">
              <Skeleton className="h-8 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
              <Skeleton className="h-8 w-1/2 mt-8" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Main export with Suspense wrapper
export default function WhatIsKCC() {
  return (
    <Suspense fallback={<WhatIsKCCLoading />}>
      <WhatIsKCCContent />
    </Suspense>
  )
}