'use client'

import { useState, useEffect, useMemo } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, MessageSquare, ChevronDown, ChevronUp } from 'lucide-react'
import AnimatedHero from '@/components/animated-hero'
import { fetchForumsData, Forum } from '@/lib/strapi'
import ImageSlider from '@/components/image-slider'
import { Button } from '@/components/ui/button'
import { useTranslation, getLanguageFromParams } from '@/lib/translations'
import { useSearchParams } from 'next/navigation'
import type { TranslationKey } from '@/lib/translations'

// Helper function to render rich text content
const renderRichText = (content: Array<{
  type: string;
  children: Array<{
    text: string;
    type: string;
  }>;
}>) => {
  if (!content || !Array.isArray(content)) return "";

  return content.map((block, index) => {
    if (block.type === "paragraph" && block.children) {
      return block.children.map(child => child.text).join(" ");
    }
    return "";
  }).filter(text => text.length > 0).join(" ");
}

// Add fallback types for forums.readMore and forums.readLess if not present in TranslationKey
// This is a temporary fix for linter errors if the keys are missing from the type
// @ts-expect-error: forums.readMore and forums.readLess may not be in TranslationKey yet
const readMoreKey: TranslationKey = 'forums.readMore'
// @ts-expect-error: forums.readMore and forums.readLess may not be in TranslationKey yet
const readLessKey: TranslationKey = 'forums.readLess'

// Component for handling expandable text
function ExpandableText({ text, maxLength = 150, t }: { text: string; maxLength?: number; t: (key: TranslationKey) => string }) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (text.length <= maxLength) {
    return <div className="text-muted-foreground">{text}</div>
  }

  return (
    <div className="text-muted-foreground">
      <div>
        {isExpanded ? text : `${text.substring(0, maxLength)}...`}
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
      >
        {isExpanded ? (
          <>
            <ChevronUp className="h-4 w-4 mr-1" />
            {t(readLessKey)}
          </>
        ) : (
          <>
            <ChevronDown className="h-4 w-4 mr-1" />
            {t(readMoreKey)}
          </>
        )}
      </Button>
    </div>
  )
}

// Mock data for forum activities
const mockForums = [
  {
    id: 1,
    title: "大灣區商業發展論壇",
    titleEn: "Greater Bay Area Business Development Forum",
    description: "探討大灣區一體化發展機遇，邀請政府官員、學者和企業家分享見解和經驗。",
    descriptionEn: "Exploring Greater Bay Area integration opportunities, with government officials, scholars and entrepreneurs sharing insights and experiences.",
    date: "2024-03-20",
    location: "深圳前海國際會議中心",
    locationEn: "Shenzhen Qianhai International Conference Center",
    participants: "400+ 商界領袖",
    participantsEn: "400+ Business Leaders",
    image: "/placeholder.svg"
  },
  {
    id: 2,
    title: "可持續發展商業論壇",
    titleEn: "Sustainable Business Development Forum",
    description: "討論企業社會責任和可持續發展策略，推動綠色商業實踐。",
    descriptionEn: "Discussing corporate social responsibility and sustainable development strategies, promoting green business practices.",
    date: "2024-04-10",
    location: "香港會議展覽中心",
    locationEn: "Hong Kong Convention and Exhibition Centre",
    participants: "350+ 企業代表",
    participantsEn: "350+ Corporate Representatives",
    image: "/placeholder.svg"
  }
]

export default function ForumsPage() {
  const searchParams = useSearchParams()
  const [forums, setForums] = useState<Forum[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  useEffect(() => {
    const loadForums = async () => {
      try {
        setLoading(true)
        console.log('Fetching forums data...')
        const forumsData = await fetchForumsData({
          populate: "*"
        })
        console.log('Fetched forums data:', forumsData)

        if (forumsData && forumsData.length > 0) {
          setForums(forumsData)
        } else {
          console.log('No forums data found')
          setForums([])
        }
      } catch (err) {
        console.error('Error fetching forums:', err)
        setError(err instanceof Error ? err.message : 'An error occurred while fetching forums data')
      } finally {
        setLoading(false)
      }
    }

    loadForums()
  }, [])

  if (error) {
    return (
      <div className="min-h-screen">
        <AnimatedHero
          title="forums.title"
          description="forums.description"
          image="/placeholder.svg"
          lang={currentLang}
        />
        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              <span className="block">發生錯誤</span>
              <span className="text-base text-red-600">An error occurred</span>
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="forums.title"
        description="forums.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">{t('forums.staticTitle')}</h2>
              <p>{t('forums.staticDescription')}</p>
            </div>
          </CardContent>
        </Card>

        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {forums.length > 0 ? (
                <div className="space-y-6">
                  <div className="border-b pb-2">
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">{t('forums.sectionTitle')}</span>
                    </h2>
                    <p className="text-muted-foreground">{t('forums.sectionSubtitle')}</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {forums.map((forum) => (
                      <Card key={forum.id} className="overflow-hidden hover:shadow-lg transition-all">
                        {/* Image Slider for multiple images */}
                        {forum.image && forum.image.length > 0 ? (
                          <ImageSlider
                            images={forum.image.map((img, index) => ({
                              id: img.id,
                              url: img.url,
                              formats: img.formats,
                              alt: img.alternativeText || `${forum.forum_title || "Forum"} - Image ${index + 1}`
                            }))}
                            alt={`${forum.forum_title || "Forum"} - Gallery`}
                            interval={3000}
                          />
                        ) : (
                          <div className="relative aspect-video">
                            <Image
                              src="/placeholder.svg"
                              alt={`${forum.forum_title || "Forum"} - Image`}
                              fill
                              className="object-cover"
                            />
                          </div>
                        )}
                        <CardContent className="p-6">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                            <Calendar className="h-4 w-4" />
                            <time>{new Date(forum.year).toLocaleDateString()}</time>
                          </div>
                          <h3 className="text-xl font-bold mb-2">{forum.forum_title}</h3>
                          <div className="space-y-2 mb-4">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <MessageSquare className="h-4 w-4" />
                              <span>Forum Discussion</span>
                            </div>
                          </div>
                          <ExpandableText
                            text={renderRichText(forum.ranking_or_highlights)}
                            maxLength={200}
                            t={t}
                          />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">{t('forums.noForums')}</p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}
