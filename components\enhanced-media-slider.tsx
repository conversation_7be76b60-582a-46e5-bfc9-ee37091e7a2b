"use client"

import { useState, useEffect, useRef } from "react"
import Image from "next/image"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface StrapiMedia {
  id: number
  url: string
  mime?: string
  previewUrl?: string | null
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  formats?: {
    large?: { url: string }
    medium?: { url: string }
    small?: { url: string }
    thumbnail?: { url: string }
  }
}

interface EnhancedMediaSliderProps {
  media: StrapiMedia[]
  alt: string
  interval?: number
  className?: string
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: StrapiMedia[]): StrapiMedia[] {
  return mediaArr.map((item: StrapiMedia) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

export default function EnhancedMediaSlider({ 
  media, 
  alt, 
  interval = 3000, 
  className = "" 
}: EnhancedMediaSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Patch media MIME types
  const patchedMedia = patchMediaMime(media)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + patchedMedia.length) % patchedMedia.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % patchedMedia.length)

  useEffect(() => {
    if (patchedMedia.length <= 1) return

    const currentMedia = patchedMedia[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [patchedMedia, currentIndex, interval])

  if (patchedMedia.length === 0) {
    return (
      <div className={`relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center ${className}`}>
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = patchedMedia[currentIndex]

  // Get the best available URL for the current media
  const getMediaUrl = (media: StrapiMedia) => {
    if (media.url) return media.url
    if (media.formats?.medium?.url) return media.formats.medium.url
    if (media.formats?.small?.url) return media.formats.small.url
    if (media.formats?.large?.url) return media.formats.large.url
    if (media.formats?.thumbnail?.url) return media.formats.thumbnail.url
    return "/placeholder.svg"
  }

  return (
    <div className={`relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center ${className}`}>
      {/* Left Arrow */}
      {patchedMedia.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={getMediaUrl(currentMedia)}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={getMediaUrl(currentMedia)}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority={currentIndex === 0}
          />
        )}
      </div>

      {/* Right Arrow */}
      {patchedMedia.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {patchedMedia.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {patchedMedia.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
